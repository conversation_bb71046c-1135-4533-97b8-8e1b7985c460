# https://github.com/bitnami/charts/blob/minio/16.0.10/bitnami/minio/values.yaml
global:
  security:
    # This is required to allow the use of the bitnamilegacy images
    allowInsecureImages: true

image:
  repository: bitnamilegacy/minio

clientImage:
  repository: bitnamilegacy/minio-client

resources:
  limits:
    memory: 2250Mi
  requests:
    cpu: 225m
    memory: 1850Mi

metrics:
  enabled: true

pdb:
  create: false

provisioning:
  enabled: true

  podAnnotations:
    # force the provisioning to run on every sync
    argocd.argoproj.io/sync-options: Force=true,Replace=true

  policies:
    - name: readwrite-no-modify
      statements:
        - effect: Deny
          actions:
            - s3:CreateBucket
            - s3:DeleteBucket
            - s3:ForceDeleteBucket
            - s3:PutBucketPolicy
            - s3:DeleteBucketPolicy
            - s3:PutBucketTagging
            - s3:PutBucketVersioning
            # TODO(CW): Uncomment this when we have removed the existing
            #           code that modifies bucket lifecycle configurations.
            # - s3:PutLifecycleConfiguration
            - s3:PutEncryptionConfiguration
            - s3:PutReplicationConfiguration
          resources:
            - arn:aws:s3:::*
        - effect: Allow
          actions:
            - s3:*
          resources:
            - arn:aws:s3:::*

  users:
    # A general purpose user for internal services
    - username: minio-internal
      password: minio-internal-password
      policies:
        - readwrite-no-modify

  buckets:
    - name: best-shots
      versioning: Suspended
      lifecycle:
        - id: BestShotsLifecycle
          expiry:
            days: 7
    - name: eyecue-images
      versioning: Suspended
      lifecycle:
        - id: ImagesLifecycle
          expiry:
            days: 7
    - name: eyecue-mosaic
      versioning: Suspended
      lifecycle:
        - id: MosaicLifecycle
          expiry:
            days: 7
    - name: validations
      versioning: Suspended
      lifecycle:
        - id: ValidationsLifecycle
          expiry:
            days: 7

  extraCommands:
    - mc ilm rule add provisioning/best-shots --expire-delete-marker
    - mc ilm rule add provisioning/eyecue-images --expire-delete-marker
    - mc ilm rule add provisioning/eyecue-mosaic --expire-delete-marker
    - mc ilm rule add provisioning/validations --expire-delete-marker

extraEnvVars:
  # solves the issue where kubectl port-forward doesn't work, see:
  # https://github.com/minio/console/issues/2539#issuecomment-1619211962
  - name: MINIO_BROWSER_LOGIN_ANIMATION
    value: 'off'

persistence:
  enabled: true
  existingClaim: minio-pvc-v2

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: "/minio/v2/metrics/cluster"
  prometheus.io/port: "9000"

volumePermissions:
  enabled: true

  image:
    repository: bitnamilegacy/os-shell

extraDeploy:
  - kind: PersistentVolumeClaim
    apiVersion: v1
    metadata:
      name: minio-pvc-v2
    spec:
      accessModes:
        - ReadWriteOnce
      resources:
        requests:
          storage: 1Gi
      storageClassName: ""
      volumeName: minio-pv-v2
  - kind: PersistentVolume
    apiVersion: v1
    metadata:
      name: minio-pv-v2
    spec:
      capacity:
        storage: 1Gi
      accessModes:
        - ReadWriteOnce
      storageClassName: ""
      volumeMode: Filesystem
      persistentVolumeReclaimPolicy: Retain
      hostPath:
        path: /media/fingermark/storage/minio-infra-v2
