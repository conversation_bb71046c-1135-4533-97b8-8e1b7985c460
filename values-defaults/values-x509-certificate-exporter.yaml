# https://github.com/enix/x509-certificate-exporter/blob/main/deploy/charts/x509-certificate-exporter/values.yaml

# NOTE: We're currently using static targets but will leave the pod annotations in place for future-proofing
# static targets are defined in https://bitbucket.org/fingermarkltd/eyecue-helm-templates/src/master/eyecue/templates/infra/victoria-metrics/vmagent-configmap.yaml

fullnameOverride: x509-certificate-exporter

# Enable TLS Secrets monitoring (Deployment)
secretsExporter:
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9793"

# Enable host path monitoring (DaemonSet)
hostPathsExporter:
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9793"

  daemonSets:
    nodes:
      watchFiles:
        - /var/lib/kubelet/pki/kubelet-client-current.pem
        - /etc/kubernetes/pki/apiserver.crt
        - /etc/kubernetes/pki/apiserver-etcd-client.crt
        - /etc/kubernetes/pki/apiserver-kubelet-client.crt
        - /etc/kubernetes/pki/ca.crt
        - /etc/kubernetes/pki/front-proxy-ca.crt
        - /etc/kubernetes/pki/front-proxy-client.crt
        - /etc/kubernetes/pki/etcd/ca.crt
        - /etc/kubernetes/pki/etcd/healthcheck-client.crt
        - /etc/kubernetes/pki/etcd/peer.crt
        - /etc/kubernetes/pki/etcd/server.crt

      watchKubeconfFiles:
        - /etc/kubernetes/admin.conf
        - /etc/kubernetes/controller-manager.conf
        - /etc/kubernetes/scheduler.conf

# Ensure vmagent scrapes via Service annotations
service:
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9793"

# Disable Prometheus Operator-specific resources and rely on vmagent scraping via pod annotations
prometheusServiceMonitor:
  create: false

prometheusRules:
  create: false
