# https://github.com/bitnami/charts/blob/kube-state-metrics/5.0.9/bitnami/kube-state-metrics/values.yaml
global:
  security:
    # This is required to allow the use of the bitnamilegacy images
    allowInsecureImages: true

image:
  repository: bitnamilegacy/kube-state-metrics

pdb:
  create: false

kubeResources:
  # Disabling these as they are not compatible with K8s 1.20
  cronjobs: false
  poddisruptionbudgets: false
  horizontalpodautoscalers: false
