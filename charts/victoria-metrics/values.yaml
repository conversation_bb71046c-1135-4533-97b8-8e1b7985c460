persistentVolumes:
  victoriaMetrics:
    name: victoria-metrics-server-pv
    hostPath:
      path: /media/fingermark/storage/victoria-metrics-server-infra

persistentVolumeClaims:
  victoriaMetrics:
    name: victoria-metrics-server-pvc
    volumeName: victoria-metrics-server-pv

configMaps:
  vmAlertmanagerConfig:
    name: infra-vm-alertmanager-config
    data:
      alertmanager.yaml: |
        {{- .Files.Get "files/vmalert/alertmanager/alertmanager.yml" | nindent 4 }}

      alertmanager.tmpl: |
        {{- .Files.Get "files/vmalert/alertmanager/alertmanager.tmpl" | nindent 4 }}

# https://github.com/VictoriaMetrics/helm-charts/blob/victoria-metrics-single-0.12.2/charts/victoria-metrics-single/values.yaml
victoriaMetrics:
  enabled: true

  # as we are using the "victoriaMetrics" alias the chart tries to create a service
  # account with a capital letter, which is not allowed, so we have to override it here
  nameOverride: victoria-metrics

  server:
    persistentVolume:
      existingClaim: victoria-metrics-server-pvc

# https://github.com/VictoriaMetrics/helm-charts/blob/victoria-metrics-agent-0.14.2/charts/victoria-metrics-agent/values.yaml
victoriaMetricsAgent:
  enabled: true

  # as we are using the "victoriaMetricsAgent" alias the chart tries to create a service
  # account with a capital letter, which is not allowed, so we have to override it here
  nameOverride: victoria-metrics-agent

  extraArgs:
    remoteWrite.flushInterval: 5s
    remoteWrite.sendTimeout: 30s

  remoteWrite:
    - url: https://ec2-18-208-145-252.compute-1.amazonaws.com:8427/api/v1/write
      tlsInsecureSkipVerify: true
      # 10/10 for security
      basicAuth.username: local-single-node
      basicAuth.password: my-houst
      urlRelabelConfig:
        # Only keep the metrics that match the regex
        - action: keep
          source_labels: [__name__]
          regex: '^(eyecue|eyeq|departures|aggregated|validation|moonfire|argocd|x509)\w+'
        # Create a new label 'site_id' from the value of the 'node' label
        - action: replace
          source_labels: [node]
          target_label: "site_id"
          replacement: "$1"

    - url: http://infra-victoria-metrics-server.infra.svc.cluster.local:8428/api/v1/write

# https://github.com/VictoriaMetrics/helm-charts/blob/victoria-metrics-alert-0.12.2/charts/victoria-metrics-alert/values.yaml
victoriaMetricsAlert:
  enabled: true

  # as we are using the "victoriaMetricsAlert" alias the chart tries to create a service
  # account with a capital letter, which is not allowed, so we have to override it here
  nameOverride: victoria-metrics-alert

  server:
    configMap: infra-vm-alert-config
    baseUrl: http://localhost:9093
    extraArgs:
      rule: /config/*.yml
    datasource:
      url: http://infra-victoria-metrics-server.infra.svc.cluster.local:8428
    env:
      - name: SITE_ID
        valueFrom:
          fieldRef:
            fieldPath: spec.nodeName

  alertmanager:
    enabled: true
    configMap: infra-vm-alertmanager-config
